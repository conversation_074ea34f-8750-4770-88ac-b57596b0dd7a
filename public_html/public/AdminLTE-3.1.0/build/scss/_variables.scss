//
// Core: Variables
//

// COLORS
// --------------------------------------------------------
$blue: #0073b7 !default;
$lightblue: #3c8dbc !default;
$navy: #001f3f !default;
$teal: #39cccc !default;
$olive: #3d9970 !default;
$lime: #01ff70 !default;
$orange: #ff851b !default;
$fuchsia: #f012be !default;
$purple: #605ca8 !default;
$maroon: #d81b60 !default;
$black: #111 !default;
$gray-x-light: #d2d6de !default;

$colors: map-merge(
  (
    "lightblue": $lightblue,
    "navy": $navy,
    "olive": $olive,
    "lime": $lime,
    "fuchsia": $fuchsia,
    "maroon": $maroon,
  ),
  $colors
);

// LAYOUT
// --------------------------------------------------------

$font-size-root: 1rem !default;

// Sidebar
$sidebar-width: 250px !default;
$sidebar-padding-x: .5rem !default;
$sidebar-padding-y: 0 !default;
$sidebar-custom-height: 4rem !default;
$sidebar-custom-height-lg: 6rem !default;
$sidebar-custom-height-xl: 8rem !default;
$sidebar-custom-padding-x: .85rem !default;
$sidebar-custom-padding-y: .5rem !default;

// Boxed layout maximum width
$boxed-layout-max-width: 1250px !default;

// Body background (Affects main content background only)
$main-bg: #f4f6f9 !default;

$dark-main-bg: lighten($dark, 7.5%) !important;

// Content padding
$content-padding-y: 0 !default;
$content-padding-x: $navbar-padding-x !default;

// IMAGE SIZES
// --------------------------------------------------------
$img-size-sm: 1.875rem !default;
$img-size-md: 3.75rem !default;
$img-size-lg: 6.25rem !default;
$img-size-push: .625rem !default;

// MAIN HEADER
// --------------------------------------------------------
$main-header-bottom-border-width: $border-width !default;
$main-header-bottom-border-color: $gray-300 !default;
$main-header-bottom-border: $main-header-bottom-border-width solid $main-header-bottom-border-color !default;
$main-header-link-padding-y: $navbar-padding-y !default;
$main-header-height-inner: ($nav-link-height + ($main-header-link-padding-y * 2)) !default;
$main-header-height: calc(#{$main-header-height-inner} + #{$main-header-bottom-border-width}) !default;
$nav-link-sm-padding-y: .35rem !default;
$nav-link-sm-height: ($font-size-sm * $line-height-sm + $nav-link-sm-padding-y * 1.785) !default;
$main-header-height-sm-inner: ($nav-link-sm-height + ($main-header-link-padding-y * 2)) !default;
$main-header-height-sm: calc(#{$main-header-height-sm-inner} + #{$main-header-bottom-border-width}) !default;


// Main header skins
$main-header-dark-form-control-bg: $gray-800 !default;
$main-header-dark-form-control-focused-bg: $gray-700 !default;
$main-header-dark-form-control-focused-color: $gray-400 !default;
$main-header-dark-form-control-border-color: $gray-600 !default;
$main-header-dark-form-control-focused-border-color: $gray-600 !default;
$main-header-dark-placeholder-color: rgba($white, .6) !default;

$main-header-light-form-control-bg: darken($gray-200, 5%) !default;
$main-header-light-form-control-focused-bg: darken($gray-200, 7.5%) !default;
$main-header-light-form-control-focused-color: $gray-400 !default;
$main-header-light-form-control-border-color: $gray-400 !default;
$main-header-light-form-control-focused-border-color: darken($gray-400, 2.5%) !default;
$main-header-light-placeholder-color: rgba(0, 0, 0, .6) !default;

// MAIN FOOTER
// --------------------------------------------------------
$main-footer-padding: 1rem !default;
$main-footer-padding-sm: $main-footer-padding * .812 !default;
$main-footer-border-top-width: 1px !default;
$main-footer-border-top-color: $gray-300 !default;
$main-footer-border-top: $main-footer-border-top-width solid $main-footer-border-top-color !default;
$main-footer-height-inner: (($font-size-root * $line-height-base) + ($main-footer-padding * 2)) !default;
$main-footer-height: calc(#{$main-footer-height-inner} + #{$main-footer-border-top-width}) !default;
$main-footer-height-sm-inner: (($font-size-sm * $line-height-base) + ($main-footer-padding-sm * 2)) !default;
$main-footer-height-sm: calc(#{$main-footer-height-sm-inner} + #{$main-footer-border-top-width}) !default;
$main-footer-bg: $white !default;

// SIDEBAR SKINS
// --------------------------------------------------------

// Dark sidebar
$sidebar-dark-bg: $dark !default;
$sidebar-dark-hover-bg: rgba(255, 255, 255, .1) !default;
$sidebar-dark-color: #c2c7d0 !default;
$sidebar-dark-hover-color: $white !default;
$sidebar-dark-active-color: $white !default;
$sidebar-dark-submenu-bg: transparent !default;
$sidebar-dark-submenu-color: #c2c7d0 !default;
$sidebar-dark-submenu-hover-color: $white !default;
$sidebar-dark-submenu-hover-bg: $sidebar-dark-hover-bg !default;
$sidebar-dark-submenu-active-color: $sidebar-dark-bg !default;
$sidebar-dark-submenu-active-bg: rgba(255, 255, 255, .9) !default;

// Light sidebar
$sidebar-light-bg: $white !default;
$sidebar-light-hover-bg: rgba($black, .1) !default;
$sidebar-light-color: $gray-800 !default;
$sidebar-light-hover-color: $gray-900 !default;
$sidebar-light-active-color: $black !default;
$sidebar-light-submenu-bg: transparent !default;
$sidebar-light-submenu-color: #777 !default;
$sidebar-light-submenu-hover-color: $black !default;
$sidebar-light-submenu-hover-bg: $sidebar-light-hover-bg !default;
$sidebar-light-submenu-active-color: $sidebar-light-hover-color !default;
$sidebar-light-submenu-active-bg: $sidebar-light-submenu-hover-bg !default;

// SIDEBAR MINI
// --------------------------------------------------------
$sidebar-mini-width: ($nav-link-padding-x + $sidebar-padding-x + .8rem) * 2 !default;
$sidebar-nav-icon-width: $sidebar-mini-width - (($sidebar-padding-x + $nav-link-padding-x) * 2) !default;
$sidebar-user-image-width: $sidebar-nav-icon-width + ($nav-link-padding-x / 2) !default;

// CONTROL SIDEBAR
// --------------------------------------------------------
$control-sidebar-width: $sidebar-width !default;

// Cards
// --------------------------------------------------------
$card-border-color: $gray-100 !default;
$card-dark-border-color: lighten($gray-900, 10%) !default;
$card-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2) !default;
$card-title-font-size: 1.1rem !default;
$card-title-font-size-sm: 1rem !default;
$card-title-font-weight: $font-weight-normal !default;
$card-nav-link-padding-sm-y: .4rem !default;
$card-nav-link-padding-sm-x: .8rem !default;
$card-img-size: $img-size-sm !default;

// PROGRESS BARS
// --------------------------------------------------------
$progress-bar-border-radius: 1px !default;

// DIRECT CHAT
// --------------------------------------------------------
$direct-chat-default-msg-bg: $gray-x-light !default;
$direct-chat-default-font-color: #444 !default;
$direct-chat-default-msg-border-color: $gray-x-light !default;

// Z-INDEX
// --------------------------------------------------------
$zindex-main-header: $zindex-fixed + 4 !default;
$zindex-main-sidebar: $zindex-fixed + 8 !default;
$zindex-main-footer: $zindex-fixed + 2 !default;
$zindex-control-sidebar: $zindex-fixed + 1 !default;
$zindex-toasts: $zindex-main-sidebar + 2 !default;
$zindex-preloader: 9999 !default;

// TRANSITIONS SETTINGS
// --------------------------------------------------------

// Transition global options
$transition-speed: .3s !default;
$transition-fn: ease-in-out !default;

// TEXT
// --------------------------------------------------------
$font-size-xs: ($font-size-base * .75) !default;
$font-size-xl: ($font-size-base * 2) !default;


// BUTTON
// --------------------------------------------------------
$button-default-background-color: $gray-100 !default;
$button-default-color: #444 !default;
$button-default-border-color: #ddd !default;

$button-padding-y-xs: .125rem !default;
$button-padding-x-xs: .25rem !default;
$button-line-height-xs: $line-height-sm !default;
$button-font-size-xs: ($font-size-base * .75) !default;
$button-border-radius-xs: .15rem !default;


// ELEVATION
// --------------------------------------------------------
$elevations: ();
$elevations: map-merge(
  (
    1: unquote("0 1px 3px " + rgba($black, .12) + ", 0 1px 2px " + rgba($black, .24)),
    2: unquote("0 3px 6px " + rgba($black, .16) + ", 0 3px 6px " + rgba($black, .23)),
    3: unquote("0 10px 20px " + rgba($black, .19) + ", 0 6px 6px " + rgba($black, .23)),
    4: unquote("0 14px 28px " + rgba($black, .25) + ", 0 10px 10px " + rgba($black, .22)),
    5: unquote("0 19px 38px " + rgba($black, .3) + ", 0 15px 12px " + rgba($black, .22)),
  ),
  $elevations
);

// RIBBON
// --------------------------------------------------------
$ribbon-border-size: 3px !default;
$ribbon-line-height: 100% !default;
$ribbon-padding: .375rem 0 !default;
$ribbon-font-size: .8rem !default;
$ribbon-width: 90px !default;
$ribbon-wrapper-size: 70px !default;
$ribbon-top: 10px !default;
$ribbon-right: -2px !default;
$ribbon-lg-wrapper-size: 120px !default;
$ribbon-lg-width: 160px !default;
$ribbon-lg-top: 26px !default;
$ribbon-lg-right: 0 !default;
$ribbon-xl-wrapper-size: 180px !default;
$ribbon-xl-width: 240px !default;
$ribbon-xl-top: 47px !default;
$ribbon-xl-right: 4px !default;
