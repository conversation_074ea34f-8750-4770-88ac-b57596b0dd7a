name: Lint

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - "**"

env:
  FORCE_COLOR: 2
  NODE: 14.x

jobs:
  run:
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: "${{ env.NODE }}"

      - name: Set up npm cache
        uses: actions/cache@v2
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-v${{ env.NODE }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}}
          restore-keys: |
            ${{ runner.os }}-node-v${{ env.NODE }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}
            ${{ runner.os }}-node-v${{ env.NODE }}-

      - name: Install npm dependencies
        run: npm ci

      - name: Run lint
        run: npm run lint
