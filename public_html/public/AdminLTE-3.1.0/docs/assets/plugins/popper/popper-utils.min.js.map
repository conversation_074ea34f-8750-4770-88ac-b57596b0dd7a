{"version": 3, "file": "popper-utils.min.js", "sources": ["../src/utils/getStyleComputedProperty.js", "../src/utils/getParentNode.js", "../src/utils/getScrollParent.js", "../src/utils/getReferenceNode.js", "../src/utils/isBrowser.js", "../src/utils/isIE.js", "../src/utils/getOffsetParent.js", "../src/utils/isOffsetContainer.js", "../src/utils/getRoot.js", "../src/utils/findCommonOffsetParent.js", "../src/utils/getScroll.js", "../src/utils/includeScroll.js", "../src/utils/getBordersSize.js", "../src/utils/getWindowSizes.js", "../src/utils/getClientRect.js", "../src/utils/getBoundingClientRect.js", "../src/utils/getOffsetRectRelativeToArbitraryNode.js", "../src/utils/getViewportOffsetRectRelativeToArtbitraryNode.js", "../src/utils/isFixed.js", "../src/utils/getFixedPositionOffsetParent.js", "../src/utils/getBoundaries.js", "../src/utils/computeAutoPlacement.js", "../src/utils/debounce.js", "../src/utils/find.js", "../src/utils/findIndex.js", "../src/utils/getOffsetRect.js", "../src/utils/getOuterSizes.js", "../src/utils/getOppositePlacement.js", "../src/utils/getPopperOffsets.js", "../src/utils/getReferenceOffsets.js", "../src/utils/getSupportedPropertyName.js", "../src/utils/isFunction.js", "../src/utils/isModifierEnabled.js", "../src/utils/isModifierRequired.js", "../src/utils/isNumeric.js", "../src/utils/getWindow.js", "../src/utils/removeEventListeners.js", "../src/utils/runModifiers.js", "../src/utils/setAttributes.js", "../src/utils/setStyles.js", "../src/utils/setupEventListeners.js", "../src/utils/index.js"], "sourcesContent": ["/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const window = element.ownerDocument.defaultView;\n  const css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body\n    case '#document':\n      return element.body\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nexport default function getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n", "export default typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n", "import isBrowser from './isBrowser';\n\nconst isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nconst isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nexport default function isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  const noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  let offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    parseFloat(styles[`border${sideA}Width`]) +\n    parseFloat(styles[`border${sideB}Width`])\n  );\n}\n", "import isIE from './isIE';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE(10)\n      ? (parseInt(html[`offset${axis}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]))\n    : 0 \n  );\n}\n\nexport default function getWindowSizes(document) {\n  const body = document.body;\n  const html = document.documentElement;\n  const computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE from './isIE';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    }\n    else {\n      rect = element.getBoundingClientRect();\n    }\n  }\n  catch(e){}\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  const width =\n    sizes.width || element.clientWidth || result.width;\n  const height =\n    sizes.height || element.clientHeight || result.height;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE from './isIE';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  const isIE10 = runIsIE(10);\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if(fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = parseFloat(styles.marginTop);\n    const marginLeft = parseFloat(styles.marginLeft);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10 && !fixedPosition\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  const parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nexport default function getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n   if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  let el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getReferenceNode from './getReferenceNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement,\n  fixedPosition = false\n) {\n  // NOTE: 1 DOM access here\n\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport' ) {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  }\n\n  else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent,\n      fixedPosition\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes(popper.ownerDocument);\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  const isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0; \n  boundaries.top += isPaddingNumber ? padding : padding.top || 0; \n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0; \n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0; \n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "import isBrowser from './isBrowser';\n\nconst timeoutDuration = (function(){\n  const longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}());\n\nexport function microtaskDebounce(fn) {\n  let called = false\n  return () => {\n    if (called) {\n      return\n    }\n    called = true\n    window.Promise.resolve().then(() => {\n      called = false\n      fn()\n    })\n  }\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nconst supportsMicroTasks = isBrowser && window.Promise\n\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsMicroTasks\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import getWindowSizes from './getWindowSizes';\nimport getClientRect from './getClientRect';\n\n/**\n * Get the position of the given element, relative to its offset parent\n * @method\n * @memberof Popper.Utils\n * @param {Element} element\n * @return {Object} position - Coordinates of the element and its `scrollTop`\n */\nexport default function getOffsetRect(element) {\n  let elementRect;\n  if (element.nodeName === 'HTML') {\n    const { width, height } = getWindowSizes(element.ownerDocument);\n    elementRect = {\n      width,\n      height,\n      left: 0,\n      top: 0,\n    };\n  } else {\n    elementRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight,\n      left: element.offsetLeft,\n      top: element.offsetTop,\n    };\n  }\n\n  // position\n  return getClientRect(elementRect);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const window = element.ownerDocument.defaultView;\n  const styles = window.getComputedStyle(element);\n  const x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  const y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\nimport getReferenceNode from './getReferenceNode';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference, fixedPosition = null) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nexport default function getWindow(element) {\n  const ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n", "import getWindow from './getWindow';\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier['function']) { // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "import getScrollParent from './getScrollParent';\nimport getWindow from './getWindow';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import computeAutoPlacement from './computeAutoPlacement';\nimport debounce from './debounce';\nimport findIndex from './findIndex';\nimport getBordersSize from './getBordersSize';\nimport getBoundaries from './getBoundaries';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport getClientRect from './getClientRect';\nimport getOffsetParent from './getOffsetParent';\nimport getOffsetRect from './getOffsetRect';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getOuterSizes from './getOuterSizes';\nimport getParentNode from './getParentNode';\nimport getPopperOffsets from './getPopperOffsets';\nimport getReferenceOffsets from './getReferenceOffsets';\nimport getScroll from './getScroll';\nimport getScrollParent from './getScrollParent';\nimport getStyleComputedProperty from './getStyleComputedProperty';\nimport getSupportedPropertyName from './getSupportedPropertyName';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport isFunction from './isFunction';\nimport isModifierEnabled from './isModifierEnabled';\nimport isModifierRequired from './isModifierRequired';\nimport isNumeric from './isNumeric';\nimport removeEventListeners from './removeEventListeners';\nimport runModifiers from './runModifiers';\nimport setAttributes from './setAttributes';\nimport setStyles from './setStyles';\nimport setupEventListeners from './setupEventListeners';\n\n/** @namespace Popper.Utils */\nexport {\n  computeAutoPlacement,\n  debounce,\n  findIndex,\n  getBordersSize,\n  getBoundaries,\n  getBoundingClientRect,\n  getClientRect,\n  getOffsetParent,\n  getOffsetRect,\n  getOffsetRectRelativeToArbitraryNode,\n  getOuterSizes,\n  getParentNode,\n  getPopperOffsets,\n  getReferenceOffsets,\n  getScroll,\n  getScrollParent,\n  getStyleComputedProperty,\n  getSupportedPropertyName,\n  getWindowSizes,\n  isFixed,\n  isFunction,\n  isModifierEnabled,\n  isModifierRequired,\n  isNumeric,\n  removeEventListeners,\n  runModifiers,\n  setAttributes,\n  setStyles,\n  setupEventListeners,\n};\n\n// This is here just for backward compatibility with versions lower than v1.10.3\n// you should import the utilities using named exports, if you want them all use:\n// ```\n// import * as PopperUtils from 'popper-utils';\n// ```\n// The default export will be removed in the next major version.\nexport default {\n  computeAutoPlacement,\n  debounce,\n  findIndex,\n  getBordersSize,\n  getBoundaries,\n  getBoundingClientRect,\n  getClientRect,\n  getOffsetParent,\n  getOffsetRect,\n  getOffsetRectRelativeToArbitraryNode,\n  getOuterSizes,\n  getParentNode,\n  getPopperOffsets,\n  getReferenceOffsets,\n  getScroll,\n  getScrollParent,\n  getStyleComputedProperty,\n  getSupportedPropertyName,\n  getWindowSizes,\n  isFixed,\n  isFunction,\n  isModifierEnabled,\n  isModifierRequired,\n  isNumeric,\n  removeEventListeners,\n  runModifiers,\n  setAttributes,\n  setStyles,\n  setupEventListeners,\n};\n"], "names": ["element", "nodeType", "window", "ownerDocument", "defaultView", "css", "getComputedStyle", "property", "nodeName", "parentNode", "host", "document", "body", "overflow", "overflowX", "overflowY", "getStyleComputedProperty", "test", "getScrollParent", "getParentNode", "reference", "referenceNode", "navigator", "isIE10", "<PERSON><PERSON><PERSON><PERSON>", "userAgent", "version", "isIE11", "documentElement", "noOffsetParent", "isIE", "offsetParent", "nextElement<PERSON><PERSON>ling", "indexOf", "getOffsetParent", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "getRoot", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "isOffsetContainer", "element1root", "findCommonOffsetParent", "side", "upperSide", "html", "scrollingElement", "subtract", "scrollTop", "getScroll", "scrollLeft", "modifier", "top", "bottom", "left", "right", "sideA", "axis", "sideB", "parseFloat", "styles", "Math", "max", "parseInt", "computedStyle", "getSize", "offsets", "width", "height", "rect", "getBoundingClientRect", "result", "sizes", "getWindowSizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getBordersSize", "getClientRect", "fixedPosition", "runIsIE", "isHTML", "parent", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "includeScroll", "excludeScroll", "relativeOffset", "getOffsetRectRelativeToArbitraryNode", "innerWidth", "innerHeight", "offset", "isFixed", "parentElement", "el", "boundaries", "getFixedPositionOffsetParent", "getReferenceNode", "boundariesElement", "getViewportOffsetRectRelativeToArtbitraryNode", "boundariesNode", "popper", "padding", "isPaddingNumber", "placement", "getBoundaries", "rects", "refRect", "sorted<PERSON>reas", "Object", "keys", "map", "key", "getArea", "sort", "b", "area", "a", "filtered<PERSON><PERSON>s", "filter", "computedPlacement", "length", "variation", "split", "timeoutDuration", "longerTimeoutBrowsers", "i", "called", "Promise", "resolve", "then", "scheduled", "supportsMicroTasks", "Array", "prototype", "find", "arr", "findIndex", "cur", "match", "obj", "elementRect", "offsetLeft", "offsetTop", "x", "marginBottom", "y", "marginRight", "hash", "replace", "matched", "popperRect", "getOuterSizes", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "referenceOffsets", "getOppositePlacement", "commonOffsetParent", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "slice", "prefix", "to<PERSON><PERSON><PERSON>", "style", "functionToCheck", "getType", "toString", "call", "modifiers", "some", "name", "enabled", "requesting", "isRequired", "requested", "warn", "n", "isNaN", "isFinite", "removeEventListener", "state", "updateBound", "scrollParents", "for<PERSON>ach", "target", "scrollElement", "eventsEnabled", "modifiersToRun", "ends", "fn", "isFunction", "data", "value", "attributes", "removeAttribute", "setAttribute", "prop", "unit", "isNumeric", "isBody", "addEventListener", "passive", "push"], "mappings": ";;;GAOA,eAAoE,IACzC,CAArBA,KAAQC,uBAINC,GAASF,EAAQG,aAARH,CAAsBI,YAC/BC,EAAMH,EAAOI,gBAAPJ,GAAiC,IAAjCA,QACLK,GAAWF,IAAXE,GCPT,aAA+C,OACpB,MAArBP,KAAQQ,QADiC,GAItCR,EAAQS,UAART,EAAsBA,EAAQU,KCDvC,aAAiD,IAE3C,SACKC,UAASC,YAGVZ,EAAQQ,cACT,WACA,aACIR,GAAQG,aAARH,CAAsBY,SAC1B,kBACIZ,GAAQY,WAIb,CAAEC,UAAF,CAAYC,WAAZ,CAAuBC,WAAvB,EAAqCC,KAfI,MAgB3C,yBAAwBC,IAAxB,CAA6BJ,KAA7B,CAhB2C,GAoBxCK,EAAgBC,IAAhBD,ECvBT,aAAoD,OAC3CE,IAAaA,EAAUC,aAAvBD,CAAuCA,EAAUC,aAAjDD,GCRT,MAAiC,WAAlB,QAAOlB,OAAP,EAAqD,WAApB,QAAOS,SAAxC,EAAyF,WAArB,QAAOW,UAA1F,mECGMC,EAASC,GAAa,UAAUP,IAAV,CAAeK,UAAUG,SAAzB,EAS5B,aAAsC,OACpB,GAAZC,IADgC,GAIpB,EAAZA,IAJgC,GAO7BC,KCVT,aAAiD,IAC3C,SACKhB,UAASiB,qBAGZC,GAAiBC,EAAK,EAALA,EAAWnB,SAASC,IAApBkB,CAA2B,QAG9CC,GAAe/B,EAAQ+B,YAAR/B,EAAwB,KARI,KAUxC+B,OAAmC/B,EAAQgC,kBAVH,IAW9B,CAAChC,EAAUA,EAAQgC,kBAAnB,EAAuCD,kBAGlDvB,GAAWuB,GAAgBA,EAAavB,SAdC,MAgB3C,IAA0B,MAAbA,IAAb,EAAiD,MAAbA,IAhBO,CAuBY,CAAC,CAA1D,uBAAsByB,OAAtB,CAA8BF,EAAavB,QAA3C,GACuD,QAAvDQ,OAAuC,UAAvCA,CAxB6C,CA0BtCkB,IA1BsC,GAiBtClC,EAAUA,EAAQG,aAARH,CAAsB4B,eAAhC5B,CAAkDW,SAASiB,6BCxBnB,MAC3C,CAAEpB,UAAF,IAD2C,MAEhC,MAAbA,IAF6C,GAMlC,MAAbA,MAAuB0B,EAAgBlC,EAAQmC,iBAAxBD,KANwB,ECKnD,aAAsC,OACZ,KAApBE,KAAK3B,UAD2B,GAE3B4B,EAAQD,EAAK3B,UAAb4B,ECGX,eAAmE,IAE7D,IAAa,CAACC,EAASrC,QAAvB,EAAmC,EAAnC,EAAgD,CAACsC,EAAStC,eACrDU,UAASiB,qBAIZY,GACJF,EAASG,uBAATH,IACAI,KAAKC,4BACDC,EAAQJ,MACRK,EAAML,MAGNM,EAAQnC,SAASoC,WAATpC,KACRqC,WAAgB,EAf2C,GAgB3DC,SAAY,EAhB+C,MAiB3D,CAAEC,yBAAF,OAIHZ,OACCC,KADDD,EAEDM,EAAMO,QAANP,UAEIQ,QAIGlB,UAIHmB,GAAehB,KAjC4C,MAkC7DgB,GAAa3C,IAlCgD,CAmCxD4C,EAAuBD,EAAa3C,IAApC4C,GAnCwD,CAqCxDA,IAAiCjB,KAAkB3B,IAAnD4C,ECzCX,aAA2CC,EAAO,KAAlD,CAAyD,MACjDC,GAAqB,KAATD,KAAiB,WAAjBA,CAA+B,aAC3C/C,EAAWR,EAAQQ,YAER,MAAbA,MAAoC,MAAbA,KAAqB,MACxCiD,GAAOzD,EAAQG,aAARH,CAAsB4B,gBAC7B8B,EAAmB1D,EAAQG,aAARH,CAAsB0D,gBAAtB1D,UAClB0D,YAGF1D,MCPT,eAAqD2D,IAArD,CAAuE,MAC/DC,GAAYC,IAAmB,KAAnBA,EACZC,EAAaD,IAAmB,MAAnBA,EACbE,EAAWJ,EAAW,CAAC,CAAZA,CAAgB,WAC5BK,KAAOJ,MACPK,QAAUL,MACVM,MAAQJ,MACRK,OAASL,MCRhB,eAAqD,MAC7CM,GAAiB,GAATC,KAAe,MAAfA,CAAwB,MAChCC,EAAkB,MAAVF,IAAmB,OAAnBA,CAA6B,eAGzCG,YAAWC,WAAQ,QAARA,CAAXD,EACAA,WAAWC,WAAQ,QAARA,CAAXD,qBCd8C,OACzCE,MAAKC,GAALD,CACL7D,WAAM,GAANA,CADK6D,CAEL7D,WAAM,GAANA,CAFK6D,CAGLhB,WAAM,GAANA,CAHKgB,CAILhB,WAAM,GAANA,CAJKgB,CAKLhB,WAAM,GAANA,CALKgB,CAML3C,EAAK,EAALA,EACK6C,SAASlB,WAAM,GAANA,CAATkB,EACHA,SAASC,WAAgC,QAATP,KAAoB,KAApBA,CAA4B,QAAnDO,CAATD,CADGA,CAEHA,SAASC,WAAgC,QAATP,KAAoB,QAApBA,CAA+B,SAAtDO,CAATD,CAHF7C,CAIE,CAVG2C,EAcT,aAAiD,MACzC7D,GAAOD,EAASC,KAChB6C,EAAO9C,EAASiB,gBAChBgD,EAAgB9C,EAAK,EAALA,GAAYxB,0BAE3B,QACGuE,EAAQ,QAARA,OADH,OAEEA,EAAQ,OAARA,OAFF,uKCfT,aAA+C,sBAGpCC,EAAQZ,IAARY,CAAeA,EAAQC,aACtBD,EAAQd,GAARc,CAAcA,EAAQE,SCGlC,aAAuD,IACjDC,SAKA,IACEnD,EAAK,EAALA,EAAU,GACL9B,EAAQkF,qBAARlF,EADK,MAEN4D,GAAYC,IAAmB,KAAnBA,EACZC,EAAaD,IAAmB,MAAnBA,IACdG,MAJO,GAKPE,OALO,GAMPD,SANO,GAOPE,QAPP,QAUSnE,EAAQkF,qBAARlF,EAXX,CAcA,QAAQ,OAEFmF,GAAS,MACPF,EAAKf,IADE,KAERe,EAAKjB,GAFG,OAGNiB,EAAKd,KAALc,CAAaA,EAAKf,IAHZ,QAILe,EAAKhB,MAALgB,CAAcA,EAAKjB,GAJd,EAQToB,EAA6B,MAArBpF,KAAQQ,QAARR,CAA8BqF,EAAerF,EAAQG,aAAvBkF,CAA9BrF,IACR+E,EACJK,EAAML,KAANK,EAAepF,EAAQsF,WAAvBF,EAAsCD,EAAOJ,MACzCC,EACJI,EAAMJ,MAANI,EAAgBpF,EAAQuF,YAAxBH,EAAwCD,EAAOH,UAE7CQ,GAAiBxF,EAAQyF,WAARzF,GACjB0F,EAAgB1F,EAAQ2F,YAAR3F,MAIhBwF,KAAiC,MAC7BhB,GAASxD,QACG4E,IAAuB,GAAvBA,CAFiB,IAGlBA,IAAuB,GAAvBA,CAHkB,GAK5Bb,QAL4B,GAM5BC,gBAGFa,qBCzDsEC,KAAuB,OAajFrB,KAAKC,GAb4E,MAC9FnD,GAASwE,EAAQ,EAARA,EACTC,EAA6B,MAApBC,KAAOzF,SAChB0F,EAAehB,KACfiB,EAAajB,KACbkB,EAAelF,KAEfsD,EAASxD,KACTqF,EAAiB9B,WAAWC,EAAO6B,cAAlB9B,EACjB+B,EAAkB/B,WAAWC,EAAO8B,eAAlB/B,EAGrBuB,IAZiG,KAavF9B,IAAMS,EAAS0B,EAAWnC,GAApBS,CAAyB,CAAzBA,CAbiF,GAcvFP,KAAOO,EAAS0B,EAAWjC,IAApBO,CAA0B,CAA1BA,CAdgF,KAgBhGK,GAAUe,EAAc,KACrBK,EAAalC,GAAbkC,CAAmBC,EAAWnC,GAA9BkC,EADqB,MAEpBA,EAAahC,IAAbgC,CAAoBC,EAAWjC,IAA/BgC,EAFoB,OAGnBA,EAAanB,KAHM,QAIlBmB,EAAalB,MAJK,CAAda,OAMNU,UAAY,IACZC,WAAa,EAMjB,MAAmB,MACfD,GAAYhC,WAAWC,EAAO+B,SAAlBhC,EACZiC,EAAajC,WAAWC,EAAOgC,UAAlBjC,IAEXP,KAAOqC,GAJM,GAKbpC,QAAUoC,GALG,GAMbnC,MAAQoC,GANK,GAObnC,OAASmC,GAPI,GAUbC,WAVa,GAWbC,oBAIRjF,GAAU,EAAVA,CACI0E,EAAO9C,QAAP8C,GADJ1E,CAEI0E,OAAqD,MAA1BG,KAAa5F,cAElCiG,uBCnDiEC,KAAuB,OAGtFjC,KAAKC,GAHiF,MAC9FjB,GAAOzD,EAAQG,aAARH,CAAsB4B,gBAC7B+E,EAAiBC,OACjB7B,EAAQN,EAAShB,EAAK6B,WAAdb,CAA2BvE,OAAO2G,UAAP3G,EAAqB,CAAhDuE,EACRO,EAASP,EAAShB,EAAK8B,YAAdd,CAA4BvE,OAAO4G,WAAP5G,EAAsB,CAAlDuE,EAETb,EAAY,EAAmC,CAAnC,CAAiBC,KAC7BC,EAAa,EAA2C,CAA3C,CAAiBD,IAAgB,MAAhBA,EAE9BkD,EAAS,KACRnD,EAAY+C,EAAe3C,GAA3BJ,CAAiC+C,EAAeJ,SADxC,MAEPzC,EAAa6C,EAAezC,IAA5BJ,CAAmC6C,EAAeH,UAF3C,QAAA,SAAA,QAORX,MCTT,aAAyC,MACjCrF,GAAWR,EAAQQ,YACR,MAAbA,MAAoC,MAAbA,iBAG2B,OAAlDQ,OAAkC,UAAlCA,gBAGEP,GAAaU,KARoB,WAYhC6F,KCbT,aAA8D,IAEvD,IAAY,CAAChH,EAAQiH,aAArB,EAAsCnF,UAClCnB,UAASiB,mBAEdsF,GAAKlH,EAAQiH,cAL2C,KAMrDC,GAAoD,MAA9ClG,OAA6B,WAA7BA,CAN+C,IAOrDkG,EAAGD,oBAEHC,IAAMvG,SAASiB,gBCExB,mBAKEkE,IALF,CAME,IAGIqB,GAAa,CAAEnD,IAAK,CAAP,CAAUE,KAAM,CAAhB,OACXnC,GAAe+D,EAAgBsB,IAAhBtB,CAAuDxC,IAA+B+D,IAA/B/D,KAGlD,UAAtBgE,OACWC,WAGV,IAECC,GACsB,cAAtBF,IAHD,IAIgBpG,EAAgBC,IAAhBD,CAJhB,CAK+B,MAA5BsG,KAAehH,QALlB,KAMkBiH,EAAOtH,aAAPsH,CAAqB7F,eANvC,GAQ8B,QAAtB0F,IARR,GASgBG,EAAOtH,aAAPsH,CAAqB7F,eATrC,IAAA,MAcGkD,GAAU8B,YAOgB,MAA5BY,KAAehH,QAAfgH,EAAsC,CAACR,KAAuB,MAC1D,CAAEhC,QAAF,CAAUD,OAAV,EAAoBM,EAAeoC,EAAOtH,aAAtBkF,IACfrB,KAAOc,EAAQd,GAARc,CAAcA,EAAQyB,SAFwB,GAGrDtC,OAASe,EAASF,EAAQd,GAH2B,GAIrDE,MAAQY,EAAQZ,IAARY,CAAeA,EAAQ0B,UAJsB,GAKrDrC,MAAQY,EAAQD,EAAQZ,IALrC,YAaQwD,GAAW,CA7CrB,MA8CMC,GAAqC,QAAnB,oBACbzD,MAAQyD,IAA4BD,EAAQxD,IAARwD,EAAgB,IACpD1D,KAAO2D,IAA4BD,EAAQ1D,GAAR0D,EAAe,IAClDvD,OAASwD,IAA4BD,EAAQvD,KAARuD,EAAiB,IACtDzD,QAAU0D,IAA4BD,EAAQzD,MAARyD,EAAkB,eC3EpD,CAAE3C,OAAF,CAASC,QAAT,EAAmB,OAC3BD,KAYT,qBAME2C,EAAU,CANZ,CAOE,IACkC,CAAC,CAA/BE,KAAU3F,OAAV2F,CAAkB,MAAlBA,gBAIET,GAAaU,WAObC,EAAQ,KACP,OACIX,EAAWpC,KADf,QAEKgD,EAAQ/D,GAAR+D,CAAcZ,EAAWnD,GAF9B,CADO,OAKL,OACEmD,EAAWhD,KAAXgD,CAAmBY,EAAQ5D,KAD7B,QAEGgD,EAAWnC,MAFd,CALK,QASJ,OACCmC,EAAWpC,KADZ,QAEEoC,EAAWlD,MAAXkD,CAAoBY,EAAQ9D,MAF9B,CATI,MAaN,OACG8D,EAAQ7D,IAAR6D,CAAeZ,EAAWjD,IAD7B,QAEIiD,EAAWnC,MAFf,CAbM,EAmBRgD,EAAcC,OAAOC,IAAPD,IACjBE,GADiBF,CACbG,eAEAN,WACGO,EAAQP,IAARO,GAJUJ,EAMjBK,IANiBL,CAMZ,OAAUM,EAAEC,IAAFD,CAASE,EAAED,IANTP,EAQdS,EAAgBV,EAAYW,MAAZX,CACpB,CAAC,CAAEjD,OAAF,CAASC,QAAT,CAAD,GACED,GAAS0C,EAAOnC,WAAhBP,EAA+BC,GAAUyC,EAAOlC,YAF9ByC,EAKhBY,EAA2C,CAAvBF,GAAcG,MAAdH,CACtBA,EAAc,CAAdA,EAAiBN,GADKM,CAEtBV,EAAY,CAAZA,EAAeI,IAEbU,EAAYlB,EAAUmB,KAAVnB,CAAgB,GAAhBA,EAAqB,CAArBA,QAEXgB,IAAqBE,MAAa,GAAbA,CAA8B,EAAnDF,ECtET,KAAMI,GAAmB,UAAU,MAC3BC,oCACD,GAAIC,GAAI,EAAGA,EAAID,EAAsBJ,OAAQK,GAAK,KACjD1H,GAAsE,CAAzDF,YAAUG,SAAVH,CAAoBW,OAApBX,CAA4B2H,IAA5B3H,QACR,SAGJ,EAPgB,CAAA,EAAzB,CAUA,aAAsC,IAChC6H,YACG,IAAM,SAAA,QAKJC,QAAQC,UAAUC,KAAK,IAAM,KAAA,IAApC,EALW,CAAb,EAYF,aAAiC,IAC3BC,YACG,IAAM,SAAA,YAGE,IAAM,KAAA,IAAjB,IAHS,CAAb,EAWF,KAAMC,GAAqBhI,GAAatB,OAAOkJ,OAA/C,CAYA,MAAgBI,KAAhB,CC1CA,eAAyC,OAEnCC,OAAMC,SAAND,CAAgBE,IAFmB,CAG9BC,EAAID,IAAJC,GAH8B,CAOhCA,EAAIjB,MAAJiB,IAAkB,CAAlBA,ECLT,iBAAoD,IAE9CH,MAAMC,SAAND,CAAgBI,gBACXD,GAAIC,SAAJD,CAAcE,KAAOA,QAArBF,OAIHG,GAAQJ,IAAUK,KAAOA,QAAjBL,QACPC,GAAI3H,OAAJ2H,ICTT,aAA+C,IACzCK,MACqB,MAArBjK,KAAQQ,SAAqB,MACzB,CAAEuE,OAAF,CAASC,QAAT,EAAoBK,EAAerF,EAAQG,aAAvBkF,IACZ,QAAA,SAAA,MAGN,CAHM,KAIP,CAJO,CAFhB,QASgB,OACLrF,EAAQyF,WADH,QAEJzF,EAAQ2F,YAFJ,MAGN3F,EAAQkK,UAHF,KAIPlK,EAAQmK,SAJD,QASTtE,MCvBT,aAA+C,MACvC3F,GAASF,EAAQG,aAARH,CAAsBI,YAC/BoE,EAAStE,EAAOI,gBAAPJ,IACTkK,EAAI7F,WAAWC,EAAO+B,SAAP/B,EAAoB,CAA/BD,EAAoCA,WAAWC,EAAO6F,YAAP7F,EAAuB,CAAlCD,EACxC+F,EAAI/F,WAAWC,EAAOgC,UAAPhC,EAAqB,CAAhCD,EAAqCA,WAAWC,EAAO+F,WAAP/F,EAAsB,CAAjCD,EACzCY,EAAS,OACNnF,EAAQyF,WAARzF,EADM,QAELA,EAAQ2F,YAAR3F,EAFK,WCLjB,aAAwD,MAChDwK,GAAO,CAAEtG,KAAM,OAAR,CAAiBC,MAAO,MAAxB,CAAgCF,OAAQ,KAAxC,CAA+CD,IAAK,QAApD,QACN4D,GAAU6C,OAAV7C,CAAkB,wBAAlBA,CAA4C8C,KAAWF,IAAvD5C,ECIT,iBAA8E,GAChEA,EAAUmB,KAAVnB,CAAgB,GAAhBA,EAAqB,CAArBA,CADgE,MAItE+C,GAAaC,KAGbC,EAAgB,OACbF,EAAW5F,KADE,QAEZ4F,EAAW3F,MAFC,EAMhB8F,EAAmD,CAAC,CAA1C,oBAAkB7I,OAAlB,IACV8I,EAAWD,EAAU,KAAVA,CAAkB,OAC7BE,EAAgBF,EAAU,MAAVA,CAAmB,MACnCG,EAAcH,EAAU,QAAVA,CAAqB,QACnCI,EAAuB,EAAsB,OAAtB,CAAW,qBAGtCC,KACAA,KAAgC,CADhCA,CAEAR,KAA0B,OACxB/C,MAEAuD,KAAkCR,KAGlCQ,EAAiBC,IAAjBD,IC1BN,iBAAsErF,EAAgB,IAAtF,CAA4F,MACpFuF,GAAqBvF,EAAgBsB,IAAhBtB,CAAuDxC,IAA+B+D,IAA/B/D,QAC3EsD,UCVT,aAA2D,MACnD0E,gCACAC,EAAYhL,EAASiL,MAATjL,CAAgB,CAAhBA,EAAmBkL,WAAnBlL,GAAmCA,EAASmL,KAATnL,CAAe,CAAfA,MAEhD,GAAI2I,GAAI,EAAGA,EAAIoC,EAASzC,OAAQK,IAAK,MAClCyC,GAASL,KACTM,EAAUD,KAAU,IAAA,GAAVA,MAC4B,WAAxC,QAAOhL,UAASC,IAATD,CAAckL,KAAdlL,mBAIN,MCXT,aAAoD,OAGhDmL,IAC2C,mBAA3CC,MAAQC,QAARD,CAAiBE,IAAjBF,ICLJ,eAAmE,OAC1DG,GAAUC,IAAVD,CACL,CAAC,CAAEE,MAAF,CAAQC,SAAR,CAAD,GAAuBA,GAAWD,KAD7BF,ECKT,iBAIE,MACMI,GAAa3C,IAAgB,CAAC,CAAEyC,MAAF,CAAD,GAAcA,KAA9BzC,EAEb4C,EACJ,CAAC,EAAD,EACAL,EAAUC,IAAVD,CAAenI,KAEXA,EAASqI,IAATrI,MACAA,EAASsI,OADTtI,EAEAA,EAASvB,KAATuB,CAAiBuI,EAAW9J,KAJhC0J,KAQE,GAAa,MACTI,QAAc,MACdE,OAAa,cACXC,QACL,6BAAA,6DAAA,eC1BP,aAAqC,OACtB,EAANC,MAAY,CAACC,MAAMpI,aAANoI,CAAbD,EAAqCE,YCH9C,aAA2C,MACnCzM,GAAgBH,EAAQG,oBACvBA,GAAgBA,EAAcC,WAA9BD,CAA4CD,OCCrD,eAA+D,aAExC2M,oBAAoB,SAAUC,EAAMC,eAGnDC,cAAcC,QAAQC,KAAU,GAC7BL,oBAAoB,SAAUC,EAAMC,YAD7C,KAKMA,YAAc,OACdC,mBACAG,cAAgB,OAChBC,mBCPR,iBAA4D,MACpDC,GAAiBC,aAEnBpB,EAAUR,KAAVQ,CAAgB,CAAhBA,CAAmBrC,IAAqB,MAArBA,GAAnBqC,WAEWe,QAAQlJ,KAAY,CAC7BA,EAAS,UAATA,CAD6B,UAEvB0I,KAAK,wDAFkB,MAI3Bc,GAAKxJ,EAAS,UAATA,GAAwBA,EAASwJ,GACxCxJ,EAASsI,OAATtI,EAAoByJ,IALS,KAS1B1I,QAAQ2C,OAAS5B,EAAc4H,EAAK3I,OAAL2I,CAAahG,MAA3B5B,CATS,GAU1Bf,QAAQ1D,UAAYyE,EAAc4H,EAAK3I,OAAL2I,CAAarM,SAA3ByE,CAVM,GAYxB0H,MAZwB,CAAnC,KCXF,eAA2D,QAClDrF,QAAiB+E,QAAQ,WAAe,MACvCS,GAAQC,KACVD,MAFyC,GAKnCE,kBALmC,GAGnCC,eAAmBF,KAH/B,GCCF,eAAmD,QAC1CzF,QAAa+E,QAAQa,KAAQ,IAC9BC,GAAO,GAIP,CAAC,CADH,oDAAsD9L,OAAtD,KAEA+L,EAAUxJ,IAAVwJ,CANgC,KAQzB,IARyB,IAU1BnC,SAAcrH,MAVxB,sBCR2E,MACrEyJ,GAAmC,MAA1B7H,KAAa5F,SACtB0M,EAASe,EAAS7H,EAAajG,aAAbiG,CAA2BhG,WAApC6N,KACRC,qBAAkC,CAAEC,UAAF,EAHkC,MAOvEjN,EAAgBgM,EAAOzM,UAAvBS,QAPuE,GAa7DkN,QAShB,mBAKE,GAEMrB,aAFN,MAGqBmB,iBAAiB,SAAUpB,EAAMC,YAAa,CAAEoB,UAAF,EAHnE,MAMMhB,GAAgBjM,gBAGpB,SACA4L,EAAMC,YACND,EAAME,iBAEFG,kBACAC,mBCyBR,MAAe,uBAAA,WAAA,YAAA,iBAAA,gBAAA,wBAAA,gBAAA,kBAAA,gBAAA,uCAAA,gBAAA,gBAAA,mBAAA,sBAAA,YAAA,kBAAA,2BAAA,2BAAA,iBAAA,UAAA,aAAA,oBAAA,qBAAA,YAAA,uBAAA,eAAA,gBAAA,YAAA,sBAAA,CAAf"}