GEM
  remote: https://rubygems.org/
  specs:
    addressable (2.7.0)
      public_suffix (>= 2.0.2, < 5.0)
    colorator (1.1.0)
    concurrent-ruby (1.1.7)
    em-websocket (0.5.2)
      eventmachine (>= 0.12.9)
      http_parser.rb (~> 0.6.0)
    eventmachine (1.2.7)
    eventmachine (1.2.7-x64-mingw32)
    ffi (1.14.1)
    ffi (1.14.1-x64-mingw32)
    forwardable-extended (2.6.0)
    http_parser.rb (0.6.0)
    i18n (1.8.5)
      concurrent-ruby (~> 1.0)
    jekyll (4.2.0)
      addressable (~> 2.4)
      colorator (~> 1.0)
      em-websocket (~> 0.5)
      i18n (~> 1.0)
      jekyll-sass-converter (~> 2.0)
      jekyll-watch (~> 2.0)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.0)
      liquid (~> 4.0)
      mercenary (~> 0.4.0)
      pathutil (~> 0.9)
      rouge (~> 3.0)
      safe_yaml (~> 1.0)
      terminal-table (~> 2.0)
    jekyll-sass-converter (2.1.0)
      sassc (> 2.0.1, < 3.0)
    jekyll-seo-tag (2.7.1)
      jekyll (>= 3.8, < 5.0)
    jekyll-sitemap (1.4.0)
      jekyll (>= 3.7, < 5.0)
    jekyll-watch (2.2.1)
      listen (~> 3.0)
    kramdown (2.3.0)
      rexml
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    liquid (4.0.3)
    listen (3.3.3)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    mercenary (0.4.0)
    pathutil (0.16.2)
      forwardable-extended (~> 2.6)
    public_suffix (4.0.6)
    rb-fsevent (0.10.4)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rexml (3.2.4)
    rouge (3.26.0)
    safe_yaml (1.0.5)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc (2.4.0-x64-mingw32)
      ffi (~> 1.9)
    terminal-table (2.0.0)
      unicode-display_width (~> 1.1, >= 1.1.1)
    unicode-display_width (1.7.0)
    wdm (0.1.1)

PLATFORMS
  ruby
  x64-mingw32

DEPENDENCIES
  jekyll (~> 4.2.0)
  jekyll-seo-tag (~> 2.7.1)
  jekyll-sitemap (~> 1.4.0)
  wdm (~> 0.1.1)

BUNDLED WITH
   1.17.3
