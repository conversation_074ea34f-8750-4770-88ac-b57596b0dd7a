<aside class="main-sidebar sidebar-dark-primary elevation-4">
  <a href="{{ '/index.html' | prepend: site.baseurl }}" class="brand-link logo-switch">
    <img src="{{ '/assets/img/logo-xs.png' | prepend: site.baseurl }}" alt="AdminLTE Docs Logo Small" class="brand-image-xl logo-xs">
    <img src="{{ '/assets/img/logo-xl.png' | prepend: site.baseurl }}" alt="AdminLTE Docs Logo Large" class="brand-image-xs logo-xl" style="left: 12px">
  </a>
  <div class="sidebar">
    <div class="form-inline mt-2">
      <div class="input-group" data-widget="sidebar-search">
        <input class="form-control form-control-sidebar" type="search" placeholder="Search" aria-label="Search">
        <div class="input-group-append">
          <button class="btn btn-sidebar">
            <i class="fas fa-search fa-fw"></i>
          </button>
        </div>
      </div>
    </div>
    <nav class="mt-2">
      <ul class="nav nav-pills nav-sidebar nav-child-indent flex-column" data-widget="treeview" role="menu">
       {% for item in site.navigation -%}
          {%- assign subitem_active = 'false' -%}
          {%- for subitem in item.subitems -%}
            {%- assign subitem_url = '/' | append: subitem.url -%}
            {%- if subitem_url == page.url -%}
              {%- assign subitem_active = 'true' -%}
            {%- endif -%}
          {%- endfor %}

          <li class="nav-item{% if subitem_active == 'true' %} menu-open{% endif %}">
            {% if item.url == 'index.html' -%}
              {%- assign item_url = '/' -%}
            {%- else -%}
              {%- assign item_url = '/' | append: item.url -%}
            {%- endif %}

            <a href="{% if item.url %}{{ item.url | prepend: "/" | prepend: site.baseurl }}{% else %}#{% endif %}" class="nav-link{% if item_url == page.url %} active{% endif %}{% if subitem_active == 'true'%} active{% endif %}">
              <i class="nav-icon {{ item.icon }}"></i>
              <p>
                {{ item.title }}
                {% if item.subitems %}<i class="right fas fa-angle-left"></i>{% endif %}
              </p>
            </a>

            {% if item.subitems -%}
              <ul class="nav nav-treeview">
              {% for subitem in item.subitems -%}
                {%- assign subitem_url = '/' | append: subitem.url -%}
                <li class="nav-item">
                  <a href="{{ subitem.url | prepend: "/" | prepend: site.baseurl }}" class="nav-link{% if subitem_url == page.url %} active{% endif %}">
                    <i class="far fa-circle nav-icon"></i>
                    <p>{{ subitem.title }}</p>
                  </a>
                </li>
              {%- endfor %}
              </ul>
            {%- endif %}
          </li>
       {% endfor %}
      </ul>
    </nav>
  </div>
</aside>
