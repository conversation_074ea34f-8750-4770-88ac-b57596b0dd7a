.error {
    color: #ff3636;
}

.dataTables_filter {
    display: none;
}

tbody tr td {
    word-break: break-word;
}

table.ajax_view tbody tr{
    cursor: pointer;
}

.ajax_view{
  cursor: pointer;
}

div.slide{
    display: none;
}

#purchase_history_table .bg-row {
    background: rgba(186,215,232,0.7);
    border: 2px solid #187afa;
    border-bottom: none

}
.bg-row-child {
    /* background: rgba(186,215,232,0.5); */
    border: 2px solid #187afa;
    border-top:none
}

table.dataTable tbody td.no-padding {
  padding: 0;
}
/* Modal import excel */
.input-file-excel {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}
.input-file-excel + label {
    background: transparent;
    color:Green;
    padding: 5px 10px;
    border: 1px solid green;
    border-radius: 5px;
    cursor: pointer;

}

.input-file-excel:focus + label,
.input-file-excel + label:hover {
      color:#fff;
      background-color: green;
      transition: all .2s linear;
}
.info-box{
    min-height:130px;
    color:#212529;
}
.info-box .info-box-icon{
    min-width: 100px;
}
/* .info-box:hover {
  background: rgba(0,0,0,0.05);
  color:#212529;
  font-size: 1.2rem!important;
  transition: all linear 0.1s;

} */
.info-box:hover .info-box-icon img{
    /* transform: rotate(turn); */
    animation: inforboxHover 0.5s linear;
}
/* End modal inport excel */

.checkbox:checked + img {
  border: 3px solid #21da11;
  position: relative;
  top: -3px;
  transform: scale(1.2);
}

.alert1{
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

/* End modal inport excel */
.coming-soon-text {
  font-weight: 600;
  text-transform: uppercase;
  color: #fff;
  text-shadow: 3px 4px #0ab39c;
}
.py-4 {
    padding-top: 1.5rem!important;
    padding-bottom: 1.5rem!important;
}
.bg-soft-success {
    background-color: rgba(10,179,156,.18)!important;
}
.fw-semibold {
  font-weight: 600!important;
}

.mb-0 {
  margin-bottom: 0!important;
}
