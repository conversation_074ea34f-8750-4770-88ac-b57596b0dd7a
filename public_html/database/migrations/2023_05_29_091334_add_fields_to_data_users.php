<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsToDataUsers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('data_users', function (Blueprint $table) {
            $table->bigInteger('win_population')->default(0)->after('award_count');
            $table->string('act_id')->nullable()->after('win_population');
            $table->bigInteger('gift_id')->default(0)->after('act_id');
            $table->bigInteger('countdown')->default(0)->after('gift_id');
            $table->integer('limit')->default(1)->after('countdown');
            $table->longText('self_id')->nullable()->after('limit');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('data_users', function (Blueprint $table) {
            //
        });
    }
}
