<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLiveMesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('live_mes', function (Blueprint $table) {
            $table->id();
            $table->string('user_token');
            $table->string('shareurl', 255)->nullable();
            $table->string('nickname', 255)->nullable();
            $table->string('redpkt_id', 255)->nullable();
            $table->integer('price')->default(0);
            $table->integer('count')->default(0);
            $table->integer('type')->nullable();
            $table->string('sub_type',255)->nullable();
            $table->integer('treasureType')->default(0);
            $table->integer('grab_condition')->nullable();
            $table->integer('grab_able')->nullable();
            $table->integer('grab_end_time')->default(0);
            $table->integer('limit')->default(1);
            $table->longText('self_id')->default(',');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('live_mes');
    }
}
