<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFieldsToUsers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->integer('limit')->default(1)->after('user_token');
            $table->string('condition_type')->nullable()->after('limit');
            $table->string('prize_type')->nullable()->after('condition_type');
            $table->integer('min_second')->default(5)->after('prize_type');
            $table->integer('max_second')->default(10)->after('min_second');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            //
        });
    }
}
