<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddClientVersionToSelfUidTrackings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('self_uid_trackings', function (Blueprint $table) {
            $table->boolean('is_old_client')->default(false)->after('user_agent')
                ->comment('Đánh dấu client cũ thiếu tham số roomCount');
            $table->text('client_notes')->nullable()->after('is_old_client')
                ->comment('<PERSON>hi chú về phiên bản client');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('self_uid_trackings', function (Blueprint $table) {
            $table->dropColumn(['is_old_client', 'client_notes']);
        });
    }
}
