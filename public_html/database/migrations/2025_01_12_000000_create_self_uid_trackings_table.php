<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSelfUidTrackingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('self_uid_trackings', function (Blueprint $table) {
            $table->id();
            $table->string('user_token')->index();
            $table->string('self_uid')->index();
            $table->bigInteger('diamond_balance')->default(0);
            $table->bigInteger('bean_balance')->default(0);
            $table->integer('room_count')->default(0);
            $table->text('raw_self_id')->nullable(); // Lưu raw selfId gốc
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->timestamps();

            // Indexes để tối ưu query
            $table->index(['user_token', 'self_uid']);
            $table->index(['self_uid', 'created_at']);
            $table->index(['user_token', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('self_uid_trackings');
    }
}
