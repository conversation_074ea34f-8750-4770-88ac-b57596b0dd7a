<?php

namespace App\Http\Controllers;

use App\Http\Requests\RegisterRequest;
use App\Http\Requests\SettingRequest;
use App\Models\User;
use App\Repositories\UserRepository;
use Exception;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    protected $repository;

    public function __construct(UserRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index()
    {
        if (request()->ajax()) {
            $users = $this->repository->getUserAll();
            return DataTables::of($users)
                ->addColumn('action' , function($row){
                    $html = '<button type="button" data-href="'.route('users.setting', $row->id).'" class="btn btn-outline-secondary btn-not-radius modal-btn setting_user" title="Cài đặt"><i class="fa fa-cog"></i></button>&nbsp;
                             <button type="button" data-href="'.route('users.edit', $row->id).'" class="btn btn-outline-info btn-not-radius modal-btn edit_user" title="Chỉnh sửa"><i class="fa fa-edit"></i></button>&nbsp;
                             <button type="button" data-href="'.route('users.destroy', $row->id).'" data-name="'.$row->name.'" class="btn btn-outline-danger btn-not-radius delete-btn delete_user" title="Xóa"><i class="fa fa-trash"></i></button>';
                    return $html;
                })
                ->addColumn('bigo' , function($row){
                    $html = '<a href="'.route('data', $row->user_token).'" class="btn btn-outline-warning btn-not-radius modal-btn view_data" title="Xem dữ liệu"><i class="fa fa-eye"></i></a>';
                    return $html;
                })
                ->addColumn('live_me' , function($row){
                    $html = '<a href="'.route('liveMe', $row->user_token).'" class="btn btn-outline-warning btn-not-radius modal-btn view_data" title="Xem dữ liệu"><i class="fa fa-eye"></i></a>';
                    return $html;
                })
                ->editColumn('avatar', function($row){
                    $html = '<img src="https://ui-avatars.com/api/?name='.$row->name.'" width="38px" height="38px" class="rounded-circle avatar">';
                    return $html;
                })
                ->editColumn('amount', '{{@number_format($amount)}} đ')
                ->rawColumns(['avatar', 'action', 'bigo', 'live_me'])
                ->make(true);
        }

        return view('admin.manager_user.index');
    }

    public function create()
    {
        return view('admin.manager_user.create');
    }

    public function store(RegisterRequest $request)
    {
        try {
            $this->repository->create($request);
            return response()->json([
                'success' => true,
                'msg' => 'Thêm thành viên thành công'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'msg' => 'Đã xảy ra lỗi!'
            ]);
        }
    }

    public function edit($id)
    {
        $user = $this->repository->getUser($id);
        return view('admin.manager_user.edit', compact('user'));
    }

    public function update(Request $request)
    {
        try {
            $this->repository->update($request);
            return response()->json([
                'success' => true,
                'msg' => 'Cập nhật thành viên thành công'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'msg' => 'Đã xảy ra lỗi!'
            ]);
        }
    }

    public function destroy($id)
    {
        return $this->repository->delete($id);
    }

    public function setting(Request $request, $id)
    {
        $users = $this->repository->getUserAll();
        $setting = $this->repository->getUser($id);
        return view('admin.manager_user.setting', compact('setting', 'users'));
    }

    public function updateSetting(Request $request)
    {
        try {
            $this->repository->updateSetting($request);
            return response()->json([
                'success' => true,
                'msg' => 'Cập nhật cài đặt thành công'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'msg' => 'Đã xảy ra lỗi!'
            ]);
        }
    }

    public function info()
    {
        return view('users.information');
    }

    public function updateInfo(SettingRequest $request)
    {
        try {
            $user = $this->repository->updateInfo($request);
            return response()->json([
                'success' => true,
                'data' => $user,
                'msg' => 'Cập nhật thông tin thành công'
            ]);
        } catch (Exception $e) {
            dd($e);
            return response()->json([
                'success' => false,
                'msg' => 'Đã xảy ra lỗi!'
            ]);
        }
    }

    public function changePassword(Request $request)
    {
        try {
            $chang_password = $this->repository->changePassword($request);
            if($chang_password){
                return response()->json([
                    'success' => true,
                    'msg' => 'Thay đổi mật khẩu thành công'
                ]);
            }
            return response()->json([
                'success' => false,
                'msg' => 'Mật khẩu cũ không chính xác'
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'success' => false,
                'msg' => 'Đã xảy ra lỗi!'
            ]);
        }

    }
    public function documentApi()
    {
        return view('document');
    }

    public static function utf8convert($str) {

        if(!$str) return false;

        $utf8 = array(

            'a'=>'á|à|ả|ã|ạ|ă|ắ|ặ|ằ|ẳ|ẵ|â|ấ|ầ|ẩ|ẫ|ậ|Á|À|Ả|Ã|Ạ|Ă|Ắ|Ặ|Ằ|Ẳ|Ẵ|Â|Ấ|Ầ|Ẩ|Ẫ|Ậ',

            'd'=>'đ|Đ',

            'e'=>'é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ|É|È|Ẻ|Ẽ|Ẹ|Ê|Ế|Ề|Ể|Ễ|Ệ',

            'i'=>'í|ì|ỉ|ĩ|ị|Í|Ì|Ỉ|Ĩ|Ị',

            'o'=>'ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ|Ó|Ò|Ỏ|Õ|Ọ|Ô|Ố|Ồ|Ổ|Ỗ|Ộ|Ơ|Ớ|Ờ|Ở|Ỡ|Ợ',

            'u'=>'ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự|Ú|Ù|Ủ|Ũ|Ụ|Ư|Ứ|Ừ|Ử|Ữ|Ự',

            'y'=>'ý|ỳ|ỷ|ỹ|ỵ|Ý|Ỳ|Ỷ|Ỹ|Ỵ',

        );

        foreach($utf8 as $ascii=>$uni) $str = preg_replace("/($uni)/i",$ascii,$str);

        return $str;

    }
}
