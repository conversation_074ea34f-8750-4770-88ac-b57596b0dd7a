<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DataUser;
use App\Models\LiveMe;
use App\Models\SelfUidTracking;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ApiController extends Controller
{

    public function importData(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'data' => 'required',
            'user_token' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), Response::HTTP_BAD_REQUEST);
        }
        $data_user = DataUser::where('user_token', $request->user_token)->pluck('act_id')->toArray();
        $user_token = User::where('user_token', $request->user_token)->first();
        if (!isset($user_token)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unknown user token'
            ],500);
        }else{
            $data = $request->data;
            $arr_data = [];
            $value = [];
            $value1 = [];
            $error = 0;
            foreach ($data as $da) {
                if (isset($da['act_id'])) {
                    if (!in_array($da['act_id'], $arr_data) && !in_array($da['act_id'], $data_user)) {
                        $arr_data[] = $da['act_id'];
                        $value[] = [
                            'user_token' => $request->user_token,
                            'user_name' => isset($da['user_name']) ? $da['user_name'] : '',
                            'host_id' => isset($da['host_id']) ? $da['host_id'] : 0,
                            'room_id' => isset($da['room_id']) ? $da['room_id'] : 0,
                            'condition_type' => isset($da['condition_type']) ? $da['condition_type'] : 0,
                            'prize_type' => isset($da['prize_type']) ? $da['prize_type'] : 0,
                            'award_count' => isset($da['award_count']) ? $da['award_count'] : 0,
                            'win_population' => isset($da['win_population']) ? $da['win_population'] : 0,
                            'gift_id' => isset($da['gift_id']) ? $da['gift_id'] : 0,
                            'countdown' => isset($da['countdown']) ? $da['countdown'] + Carbon::now()->timestamp : 0,
                            'act_id' => isset($da['act_id']) ? $da['act_id'] : '',
                            'join_type' => isset($da['join_type']) ? $da['join_type'] : 2,
                            'limit' => $user_token->limit,
                            'self_id' => ',',
                            'created_at' => Carbon::now(),
                        ];

                        if (!empty($user_token->share_room)) {
                            foreach (explode(',', $user_token->share_room) as $share) {
                                if (!in_array($da['act_id'], DataUser::where('user_token', $share)->pluck('act_id')->toArray())) {
                                    $value1[] = [
                                        'user_token' => $share,
                                        'user_name' => isset($da['user_name']) ? $da['user_name'] : '',
                                        'host_id' => isset($da['host_id']) ? $da['host_id'] : 0,
                                        'room_id' => isset($da['room_id']) ? $da['room_id'] : 0,
                                        'condition_type' => isset($da['condition_type']) ? $da['condition_type'] : 0,
                                        'prize_type' => isset($da['prize_type']) ? $da['prize_type'] : 0,
                                        'award_count' => isset($da['award_count']) ? $da['award_count'] : 0,
                                        'win_population' => isset($da['win_population']) ? $da['win_population'] : 0,
                                        'gift_id' => isset($da['gift_id']) ? $da['gift_id'] : 0,
                                        'countdown' => isset($da['countdown']) ? $da['countdown'] + Carbon::now()->timestamp : 0,
                                        'act_id' => isset($da['act_id']) ? $da['act_id'] : '',
                                        'join_type' => isset($da['join_type']) ? $da['join_type'] : 2,
                                        'limit' => $user_token->limit,
                                        'self_id' => ',',
                                        'created_at' => Carbon::now(),
                                    ];
                                }
                            }
                        }
                    }else{
                        $error++;
                    }
                }
            }

            DataUser::insert($value);

            DataUser::insert($value1);

            if ($error == 0) {
                return response()->json([
                    'status' => 'success'
                ]);
            }else{
                return response()->json([
                    'status' => 'error',
                    'msg' => 'There are '.$error. ' have duplicate data'
                ]);
            }

        }
    }

    public function getData($token, $selfid)
    {
        // Lưu thông tin tracking trước khi xử lý
        $this->saveSelfUidTracking($token, $selfid, request());

        $selfId[] = $selfid;
        $user = User::where('user_token', $token)->first();
        $data = DataUser::where('user_token', $token)
                        ->where('gift_id', 0)
                        ->where('self_id', 'not LIKE', '%'.$selfid.'%')
                        ->whereRaw("countdown - UNIX_TIMESTAMP(now()) > ?", $user->min_second)
                        ->whereRaw("countdown - UNIX_TIMESTAMP(now()) < ?", $user->max_second)
                        ->where('limit', '!=', 0)
                        ->when($user->join_type != 0, function($query) use ($user){
                            $query->where('join_type', $user->join_type);
                        })
                        ->when(!empty($user->condition_type), function($query) use ($user){
                            $query->whereIn('condition_type', explode(',', $user->condition_type));
                        })
                        ->when(!empty($user->prize_type), function($query) use ($user){
                            $query->whereIn('prize_type', explode(',', $user->prize_type));
                        })
                        ->first();

        // $ip = self::checkIp();
        // Log::channel('log_ip')->info($ip.' - '.$user->user_token.' - '.$user->name);
        if(isset($data) && ($data->award_count / $data->win_population >= $user->coins) ){
            $merge = array_merge(explode(',',$data->self_id), $selfId);
            $data->self_id = implode(',',$merge);
            $data->limit -= 1;
            $data->save();

            if ($data->condition_type == 1) {
                $condition_type = 'Send gift';
            }elseif($data->condition_type == 2){
                $condition_type = 'Share the room';
            }elseif($data->condition_type == 3){
                $condition_type = 'Say the password';
            }elseif($data->condition_type == 4){
                $condition_type = 'Join the fan group';
            }

            if ($data->prize_type == 1) {
                $prize_type = 'Diamond';
            }elseif($data->prize_type == 2){
                $prize_type = 'Bean';
            }elseif($data->prize_type == 3){
                $prize_type = 'Customize';
            }
            $join_type = '';
            if ($data->join_type == 1) {
                $join_type = 'Join fan';
            }elseif($data->join_type == 2){
                $join_type = 'No join fan';
            }

            return '{
                "status":"success",
                "user_name":"'.$data->user_name.'",
                "host_id":"'.$data->host_id.'",
                "room_id":"'.$data->room_id.'",
                "act_id":"'.$data->act_id.'",
                "gift_id":"'.$data->gift_id.'",
                "win_population":"'.$data->win_population.'",
                "condition_type":"'.$condition_type.'",
                "prize_type":"'.$prize_type.'",
                "award_count":"'.$data->award_count.'",
                "join_type":"'.$join_type.'",
                "limit":"'.$data->limit.'",
                "time":"'.date('d-m-Y H:i:s', $data->countdown).'"
            }';

        }else{
            return response()->json([
                'status' => 'error',
                'message' => 'Out of data'
            ],500);
        }
    }

    public function importLiveMe(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'data' => 'required',
            'user_token' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), Response::HTTP_BAD_REQUEST);
        }
        $data_all = LiveMe::where('user_token', $request->user_token)->get();
        $user_token = User::where('user_token', $request->user_token)->first();
        if (!isset($user_token)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unknown user token'
            ],500);
        }else{
            $data = $request->data;
            $data_user = [];
            $arr_data = [];
            $value = [];
            $error = 0;
            foreach($data_all as $dt){
                $data_user[] = $dt->redpkt_id;
            }
            foreach ($data as $da) {
                if (isset($da['redpkt_id'])) {
                    if (!in_array($da['redpkt_id'], $arr_data) && !in_array($da['redpkt_id'], $data_user)) {
                        $arr_data[] = $da['redpkt_id'];
                        $value[] = [
                            'user_token' => $request->user_token,
                            'shareurl' => isset($da['shareurl']) ? $da['shareurl'] : '',
                            'nickname' => isset($da['nickname']) ? $da['nickname'] : '',
                            'price' => isset($da['price']) ? $da['price'] : 0,
                            'count' => isset($da['count']) ? $da['count'] : 0,
                            'type' => isset($da['type']) ? $da['type'] : '',
                            'sub_type' => isset($da['sub_type']) ? $da['sub_type'] : '',
                            'treasureType' => isset($da['treasureType']) ? $da['treasureType'] : '',
                            'grab_end_time' => isset($da['grab_end_time']) ? $da['grab_end_time'] - 86400  : 0,
                            'redpkt_id' => isset($da['redpkt_id']) ? $da['redpkt_id'] : '',
                            'grab_condition' => isset($da['grab_condition']) ? $da['grab_condition'] : 0,
                            'grab_able' => isset($da['grab_able']) ? $da['grab_able'] : 0,
                            'limit' => $user_token->limit,
                            'created_at' => Carbon::now(),
                        ];
                    }else{
                        $error++;
                    }
                }
            }
            LiveMe::insert($value);
            if ($error == 0) {
                return response()->json([
                    'status' => 'success'
                ]);
            }else{
                return response()->json([
                    'status' => 'error',
                    'msg' => 'There are '.$error. ' have duplicate data'
                ]);
            }

        }
    }

    public function getLiveMe($token, $selfid)
    {
        $selfId[] = $selfid;
        $user = User::where('user_token', $token)->first();
        $data = LiveMe::where('user_token', $token)
                        ->where('self_id', 'not LIKE', '%'.$selfid.'%')
                        ->whereRaw("grab_end_time - UNIX_TIMESTAMP(now()) > ?", $user->min_second)
                        ->whereRaw("grab_end_time - UNIX_TIMESTAMP(now()) < ?", $user->max_second)
                        ->where('limit', '!=', 0)
                        ->when(!empty($user->grab_condition), function($query) use ($user){
                            $query->whereIn('grab_condition', explode(',', $user->condition_type));
                        })
                        ->when(!empty($user->grab_able), function($query) use ($user){
                            $query->whereIn('grab_able', explode(',', $user->prize_type));
                        })
                        ->first();

        if(isset($data)){
            $merge = array_merge(explode(',',$data->self_id), $selfId);
                $data->self_id = implode(',',$merge);
                $data->limit -= 1;
                $data->save();

                if ($data->grab_condition == 1) {
                    $grab_condition = 'Send gift';
                }elseif($data->grab_condition == 2){
                    $grab_condition = 'Share the room';
                }elseif($data->grab_condition == 3){
                    $grab_condition = 'Say the password';
                }elseif($data->grab_condition == 4){
                    $grab_condition = 'Join the fan group';
                }

                if ($data->grab_able == 1) {
                    $grab_able = 'Diamond';
                }elseif($data->grab_able == 2){
                    $grab_able = 'Bean';
                }elseif($data->grab_able == 3){
                    $grab_able = 'Customize';
                }

                return '{
                    "status":"success",
                    "shareurl":"'.$data->shareurl.'",
                    "nickname":"'.$data->nickname.'",
                    "redpkt_id":"'.$data->redpkt_id.'",
                    "price":"'.$data->price.'",
                    "count":"'.$data->count.'",
                    "type":"'.$data->type.'",
                    "sub_type":"'.$data->sub_type.'",
                    "treasureType":"'.$data->treasureType.'",
                    "limit":"'.$data->limit.'",
                    "time":"'.date('d-m-Y H:i:s', $data->countdown).'"
                }';

        }else{
            return response()->json([
                'status' => 'error',
                'message' => 'Out of data'
            ],500);
        }
    }

    public function deleteFile()
    {
        Cache::flush();
        unlink(storage_path('logs/laravel.log'));
    }

    public function checkIp()
    {
        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }

        return $ip;
    }

    /**
     * Lưu thông tin tracking selfUid
     * Format selfId: "selfUid_diamond_bean_roomCount" hoặc "selfUid_diamond_bean" (client cũ)
     * Chỉ lưu khi có thay đổi về Diamond, Bean hoặc Room Count
     */
    private function saveSelfUidTracking($userToken, $selfId, $request)
    {
        try {
            // Parse selfId để lấy thông tin (hỗ trợ client cũ)
            $parsedData = SelfUidTracking::parseSelfId($selfId);

            if ($parsedData) {
                // Kiểm tra xem có phải client cũ không (thiếu roomCount)
                $parts = explode('_', $selfId);
                $isOldClient = count($parts) < 4;

                // Sử dụng method createIfChanged để chỉ tạo record khi có thay đổi
                $newRecord = SelfUidTracking::createIfChanged(
                    $userToken,
                    $parsedData,
                    $selfId,
                    $this->checkIp(),
                    $request->header('User-Agent'),
                    $isOldClient
                );

                // Log khi có thay đổi được lưu
                if ($newRecord) {
                    Log::info('SelfUid tracking saved with changes', [
                        'user_token' => $userToken,
                        'self_uid' => $parsedData['self_uid'],
                        'diamond' => $parsedData['diamond_balance'],
                        'bean' => $parsedData['bean_balance'],
                        'room_count' => $parsedData['room_count'],
                        'record_id' => $newRecord->id,
                        'is_old_client' => $isOldClient,
                        'raw_self_id' => $selfId
                    ]);
                } else {
                    // Log debug khi không có thay đổi (có thể tắt trong production)
                    Log::debug('SelfUid tracking skipped - no changes', [
                        'user_token' => $userToken,
                        'self_uid' => $parsedData['self_uid'],
                        'is_old_client' => $isOldClient
                    ]);
                }
            } else {
                // Log khi không thể parse selfId
                Log::warning('Unable to parse selfId', [
                    'user_token' => $userToken,
                    'self_id' => $selfId
                ]);
            }
        } catch (\Exception $e) {
            // Log error nhưng không làm gián đoạn API
            Log::error('Error saving selfUid tracking: ' . $e->getMessage(), [
                'user_token' => $userToken,
                'self_id' => $selfId,
                'error' => $e->getTraceAsString()
            ]);
        }
    }
}
