<?php

namespace App\Http\Controllers;

use App\Repositories\LiveMeRepository;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class LiveMeController extends Controller
{
    protected $repository;

    public function __construct(LiveMeRepository $repository)
    {
        $this->repository = $repository;
    }

    public function viewLiveMe($token)
    {
        $user = $this->repository->getUser($token);
        if (request()->ajax()) {
            $data = $this->repository->viewData($token);
            return DataTables::of($data)
                ->addColumn('action' , function($row){
                    $html = '<button type="button" data-href="'.route('edit.liveMe', $row->id).'" class="btn btn-outline-info btn-not-radius modal-btn edit_data"><i class="fa fa-edit"></i></button>&nbsp;
                             <button type="button" data-href="'.route('delete.liveMe', $row->id).'" data-name="'.$row->nickname.'" class="btn btn-outline-danger btn-not-radius delete-btn delete_data" ><i class="fa fa-trash"></i></button>';
                    return $html;
                })
                ->editColumn('grab_condition', function($row){
                    $html = '';
                    if ($row->grab_condition == 1) {
                            $html = 'Send gift';
                    }elseif($row->grab_condition == 2){
                            $html = 'Share the room';
                    }elseif($row->grab_condition == 3){
                            $html = 'Say the password';
                    }elseif($row->grab_condition == 4){
                            $html = 'Join the fan group';
                    }
                    return $html;
                })
                ->editColumn('grab_able', function($row){
                    $html = '';
                    if ($row->grab_able == 1) {
                            $html = 'Diamond';
                    }elseif($row->grab_able == 2){
                            $html = 'Bean';
                    }elseif($row->grab_able == 3){
                            $html = 'Customize';
                    }
                    return $html;
                })
                ->editColumn('grab_end_time', '{{ date("d-m-Y H:i:s", $grab_end_time) }}')
                ->rawColumns(['action', 'grab_end_time', 'grab_condition', 'grab_able'])
                ->make(true);
        }

        return view('live_me.index', compact('token', 'user'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($token)
    {
        return view('live_me.create', compact('token'));
    }

    public function importData(Request $request, $token)
    {
        return $this->repository->importData($request, $token);
    }

    public function editData($id)
    {
        $data = $this->repository->editData($id);
        return view('live_me.edit', compact('data'));
    }

    public function updateData(Request $request, $id)
    {
        $this->repository->updateData($request, $id);
        return response()->json([
            'success' => true
        ],200);
    }

    public function delete($id)
    {
        return $this->repository->delete($id);
    }

    public function deleteAll(Request $request)
    {
        return $this->repository->deleteAll();
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
