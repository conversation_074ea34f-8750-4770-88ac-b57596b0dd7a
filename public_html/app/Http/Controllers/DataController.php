<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\User;
use App\Repositories\DataRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Yajra\DataTables\Facades\DataTables;

class DataController extends Controller
{

    protected $repository;

    public function __construct(DataRepository $repository)
    {
        $this->repository = $repository;
    }

    public function viewData($token)
    {
        $user = $this->repository->getUser($token);
        if (request()->ajax()) {
            $data = $this->repository->viewData($token);
            return DataTables::of($data)
                ->addColumn('action' , function($row){
                    $html = '<button type="button" data-href="'.route('edit.data', $row->id).'" class="btn btn-outline-info btn-not-radius modal-btn edit_data"><i class="fa fa-edit"></i></button>&nbsp;
                             <button type="button" data-href="'.route('delete.data', $row->id).'" data-name="'.$row->user_name.'" class="btn btn-outline-danger btn-not-radius delete-btn delete_data" ><i class="fa fa-trash"></i></button>';
                    return $html;
                })
                ->editColumn('condition_type', function($row){
                    $html = '';
                    if ($row->condition_type == 1) {
                            $html = 'Send gift';
                    }elseif($row->condition_type == 2){
                            $html = 'Share the room';
                    }elseif($row->condition_type == 3){
                            $html = 'Say the password';
                    }elseif($row->condition_type == 4){
                            $html = 'Join the fan group';
                    }
                    return $html;
                })
                ->editColumn('prize_type', function($row){
                    $html = '';
                    if ($row->prize_type == 1) {
                            $html = 'Diamond';
                    }elseif($row->prize_type == 2){
                            $html = 'Bean';
                    }elseif($row->prize_type == 3){
                            $html = 'Customize';
                    }
                    return $html;
                })
                ->editColumn('join_type', function($row){
                    $html = '';
                    if ($row->join_type == 1) {
                            $html = 'Join fan';
                    }elseif($row->join_type == 2){
                            $html = 'No join fan';
                    }
                    return $html;
                })
                ->editColumn('countdown', '{{ date("d-m-Y H:i:s", $countdown) }}')
                ->rawColumns(['action', 'countdown', 'condition_type', 'prize_type', 'join_type'])
                ->make(true);
        }

        return view('data.index', compact('token', 'user'));
    }

    public function create($token)
    {
        return view('data.create', compact('token'));
    }

    public function importData(Request $request, $token)
    {
        return $this->repository->importData($request, $token);
    }

    public function editData($id)
    {
        $data = $this->repository->editData($id);
        return view('data.edit', compact('data'));
    }

    public function updateData(Request $request, $id)
    {
        $this->repository->updateData($request, $id);
        return response()->json([
            'success' => true
        ],200);
    }

    public function delete($id)
    {
        return $this->repository->delete($id);
    }

    public function deleteAll(Request $request)
    {
        return $this->repository->deleteAll();
    }

    public function getDelete(Request $request)
    {
        $user = User::where('user_token', $request->user_token)->first();
        if($user){
            $user->get_delete = $request->get_delete;
            $user->save();
            return 1;
        }
        else
        {
            return 0;
        }

    }
}
