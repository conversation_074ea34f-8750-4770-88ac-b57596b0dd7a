<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'min_second' => 'numeric|gte:0',
            'max_second' => 'numeric|gte:min_second',
            'limit' => 'numeric|gte:1',
        ];
    }
    public function messages()
    {
        return [
            'min_second.numeric' => 'Chỉ được nhập số',
            'min_second.gte' => 'Số giây phải lớn hơn 0',
            'max_second.numeric' => 'Chỉ được nhập số',
            'max_second.gte' => 'Số giây kết thúc phải lớn hơn giây bắt đầu',
            'limit.numeric' => 'Chỉ được nhập số',
            'limit.gte' => 'Số lượt phải lớn hơn 1',
        ];
    }
}
