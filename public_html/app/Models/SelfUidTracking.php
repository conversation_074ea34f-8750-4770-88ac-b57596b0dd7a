<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SelfUidTracking extends Model
{
    use HasFactory;

    protected $table = 'self_uid_trackings';

    protected $fillable = [
        'user_token',
        'self_uid',
        'diamond_balance',
        'bean_balance',
        'room_count',
        'raw_self_id',
        'ip_address',
        'user_agent',
        'is_old_client',
        'client_notes'
    ];

    protected $casts = [
        'diamond_balance' => 'integer',
        'bean_balance' => 'integer',
        'room_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_token', 'user_token');
    }

    // Scope để lấy tracking theo user_token
    public function scopeByUserToken($query, $userToken)
    {
        return $query->where('user_token', $userToken);
    }

    // Scope để lấy tracking theo self_uid
    public function scopeBySelfUid($query, $selfUid)
    {
        return $query->where('self_uid', $selfUid);
    }

    // Scope để lấy tracking trong khoảng thời gian
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    // Method để parse selfId từ format: "selfUid_diamond_bean_roomCount"
    // Hỗ trợ client cũ có thể thiếu roomCount: "selfUid_diamond_bean"
    public static function parseSelfId($selfId)
    {
        $parts = explode('_', $selfId);

        if (count($parts) >= 3) {
            return [
                'self_uid' => $parts[0],
                'diamond_balance' => (int)$parts[1],
                'bean_balance' => (int)$parts[2],
                'room_count' => isset($parts[3]) ? (int)$parts[3] : 0 // Mặc định 0 nếu thiếu
            ];
        } elseif (count($parts) >= 2) {
            // Trường hợp chỉ có selfUid_diamond
            return [
                'self_uid' => $parts[0],
                'diamond_balance' => (int)$parts[1],
                'bean_balance' => 0, // Mặc định 0 nếu thiếu
                'room_count' => 0    // Mặc định 0 nếu thiếu
            ];
        } elseif (count($parts) >= 1) {
            // Trường hợp chỉ có selfUid
            return [
                'self_uid' => $parts[0],
                'diamond_balance' => 0, // Mặc định 0 nếu thiếu
                'bean_balance' => 0,    // Mặc định 0 nếu thiếu
                'room_count' => 0       // Mặc định 0 nếu thiếu
            ];
        }

        return null;
    }

    // Method để lấy lịch sử thay đổi của một selfUid
    public static function getHistoryBySelfUid($selfUid, $limit = 50)
    {
        return self::where('self_uid', $selfUid)
                   ->orderBy('created_at', 'desc')
                   ->limit($limit)
                   ->get();
    }

    // Method để lấy thống kê tổng quan
    public static function getStatsByUserToken($userToken)
    {
        return self::where('user_token', $userToken)
                   ->selectRaw('
                       COUNT(DISTINCT self_uid) as unique_users,
                       COUNT(*) as total_requests,
                       MAX(created_at) as last_activity
                   ')
                   ->first();
    }

    // Method để lấy bản ghi cuối cùng của một selfUid
    public static function getLastRecord($userToken, $selfUid)
    {
        return self::where('user_token', $userToken)
                   ->where('self_uid', $selfUid)
                   ->orderBy('created_at', 'desc')
                   ->first();
    }

    // Method để kiểm tra có thay đổi balance không
    public static function hasBalanceChanges($userToken, $selfUid, $newDiamond, $newBean, $newRoomCount)
    {
        $lastRecord = self::getLastRecord($userToken, $selfUid);

        if (!$lastRecord) {
            return true; // Nếu chưa có bản ghi nào thì coi như có thay đổi
        }

        // So sánh các giá trị, xử lý trường hợp null
        $diamondChanged = $lastRecord->diamond_balance != $newDiamond;
        $beanChanged = $lastRecord->bean_balance != $newBean;
        $roomCountChanged = $lastRecord->room_count != $newRoomCount;

        return ($diamondChanged || $beanChanged || $roomCountChanged);
    }

    // Method để tạo tracking record với kiểm tra thay đổi
    public static function createIfChanged($userToken, $parsedData, $rawSelfId, $ipAddress, $userAgent, $isOldClient = false)
    {
        if (self::hasBalanceChanges(
            $userToken,
            $parsedData['self_uid'],
            $parsedData['diamond_balance'],
            $parsedData['bean_balance'],
            $parsedData['room_count']
        )) {
            // Tạo client notes dựa trên format selfId
            $clientNotes = null;
            if ($isOldClient) {
                $parts = explode('_', $rawSelfId);
                if (count($parts) == 3) {
                    $clientNotes = 'Client cũ - thiếu roomCount';
                } elseif (count($parts) == 2) {
                    $clientNotes = 'Client cũ - thiếu bean và roomCount';
                } elseif (count($parts) == 1) {
                    $clientNotes = 'Client cũ - chỉ có selfUid';
                }
            }

            return self::create([
                'user_token' => $userToken,
                'self_uid' => $parsedData['self_uid'],
                'diamond_balance' => $parsedData['diamond_balance'],
                'bean_balance' => $parsedData['bean_balance'],
                'room_count' => $parsedData['room_count'],
                'raw_self_id' => $rawSelfId,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'is_old_client' => $isOldClient,
                'client_notes' => $clientNotes
            ]);
        }

        return null; // Không có thay đổi, không tạo record
    }
}
