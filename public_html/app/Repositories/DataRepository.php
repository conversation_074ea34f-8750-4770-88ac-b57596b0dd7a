<?php

namespace App\Repositories;

use App\Models\DataUser;
use App\Models\LiveMe;
use App\Models\User;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class DataRepository
{

    public static function viewData($token)
    {
        return DataUser::where('user_token', $token)
                        ->select([
                            'data_users.*'
                        ])
                        ->orderBy('countdown', 'asc')
                        ->get();
    }

    public static function getUser($token)
    {
        return User::where('user_token', $token)->first();
    }

    public function importData($request, $token)
    {
        $data_all = self::viewData($token);
        $user = self::getUser($token);
        $act_id = [];
        foreach ($data_all as $da) {
            $act_id[] = $da->act_id;
        }
        if (!in_array($request->act_id, $act_id)) {
            $time = str_replace('/', '-', $request->countdown);
            $data = new DataUser();
            $data->user_token = $token;
            $data->user_name = $request->user_name;
            $data->host_id = $request->host_id;
            $data->room_id = $request->room_id;
            $data->act_id = $request->act_id;
            $data->gift_id = $request->gift_id;
            $data->condition_type = $request->condition_type;
            $data->prize_type = $request->prize_type;
            $data->countdown = strtotime($time);
            $data->limit = $user->limit;
            $data->save();

            return response()->json([
                'success' => true,
            ]);
        }
        return response()->json([
            'success' => false,
            'msg' => 'Đã tồn tại dữ liệu'
        ],500);
    }

    public function editData($id)
    {
        return DataUser::find($id);
    }

    public function updateData($request, $id)
    {
        $time = str_replace('/', '-', $request->countdown);
        $data = DataUser::find($id);
        $data->user_name = $request->user_name;
        $data->host_id = $request->host_id;
        $data->room_id = $request->room_id;
        $data->act_id = $request->act_id;
        $data->gift_id = $request->gift_id;
        $data->condition_type = $request->condition_type;
        $data->prize_type = $request->prize_type;
        $data->countdown = strtotime($time);
        $data->save();
    }

    public function getDataFromTokenUser($token)
    {
        return DataUser::where('user_token', $token)->get();
    }

    public function delete($id)
    {
        DataUser::find($id)->delete();
        return response()->json([
            'success' => true,
        ]);
    }

    public function deleteFiles()
    {
        $storage = Storage::disk('local');
        if($storage)
        {
            foreach($storage->files() as $file) {
                $storage->delete($file);
            }
        }

    }

    public function deleteAll()
    {
      	DataUser::where('countdown', '<', Carbon::now()->timestamp)->delete();
      	LiveMe::where('grab_end_time', '<', Carbon::now()->timestamp)->delete();
    }
}
