<?php

namespace App\Repositories;

use App\Models\DataUser;
use App\Models\LiveMe;
use App\Models\User;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class LiveMeRepository
{

    public static function viewData($token)
    {
        return LiveMe::where('user_token', $token)
                        ->select([
                            'live_mes.*'
                        ])
                        ->orderBy('grab_end_time', 'asc')
                        ->get();
    }

    public static function getUser($token)
    {
        return User::where('user_token', $token)->first();
    }

    public function importData($request, $token)
    {
        $data_all = self::viewData($token);
        $user = self::getUser($token);
        $redpkt_id = [];
        foreach ($data_all as $da) {
            $redpkt_id[] = $da->redpkt_id;
        }
        if (!in_array($request->redpkt_id, $redpkt_id)) {
            $time = str_replace('/', '-', $request->grab_end_time);
            $data = new DataUser();
            $data->user_token = $token;
            $data->nickname = $request->nickname;
            $data->shareurl = $request->shareurl;
            $data->price = $request->price;
            $data->redpkt_id = $request->redpkt_id;
            $data->sub_type = $request->sub_type;
            $data->grab_condition = $request->grab_condition;
            $data->grab_able = $request->grab_able;
            $data->grab_end_time = strtotime($time);
            $data->limit = $user->limit;
            $data->save();

            return response()->json([
                'success' => true,
            ]);
        }
        return response()->json([
            'success' => false,
            'msg' => 'Đã tồn tại dữ liệu'
        ],500);
    }

    public function editData($id)
    {
        return LiveMe::find($id);
    }

    public function updateData($request, $id)
    {
        $time = str_replace('/', '-', $request->grab_end_time);
        $data = LiveMe::find($id);
        $data->nickname = $request->nickname;
        $data->shareurl = $request->shareurl;
        $data->price = $request->price;
        $data->redpkt_id = $request->redpkt_id;
        $data->sub_type = $request->sub_type;
        $data->grab_condition = $request->grab_condition;
        $data->grab_able = $request->grab_able;
        $data->grab_end_time = strtotime($time);
        $data->grab_end_time = strtotime($time);
        $data->save();
    }

    public function getDataFromTokenUser($token)
    {
        return LiveMe::where('user_token', $token)->get();
    }

    public function delete($id)
    {
        LiveMe::find($id)->delete();
        return response()->json([
            'success' => true,
        ]);
    }

    public function deleteFiles()
    {
        $storage = Storage::disk('local');
        if($storage)
        {
            foreach($storage->files() as $file) {
                $storage->delete($file);
            }
        }

    }

    public function deleteAll()
    {
        return LiveMe::where('countdown', '<', Carbon::now()->timestamp)->delete();
    }
}
