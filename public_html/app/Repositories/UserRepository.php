<?php

namespace App\Repositories;

use App\Models\Bank;
use App\Models\Category;
use App\Models\HistoryTransaction;
use App\Models\ServiceBill;
use App\Models\Setting;
use App\Models\User;
use App\Models\UserTransaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserRepository
{
    /**
     * Get member collection paginate.
     *
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */

    public function getUserAll()
    {
        return User::latest('id')->get();
    }

    public function getUser($id)
    {
        return User::find($id);
    }

    public function getAmount()
    {
        return User::where('id', Auth::user()->id)->select('amount')->get();
    }

    public function create($request)
    {
        User::create([
            'name' => $request->name,
            'username' => $request->username,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'recovery_password' => $request->password,
            'user_token' =>  Str::random(5),
        ]);
    }

    public function update($request)
    {
        $user = User::find($request->id);
        $user_data = $request->only(['name', 'email', 'phone', 'amount']);
        $user_data['amount'] = str_replace(',','', $request->amount);
        $user_data['password'] = $request->password ? Hash::make($request->password) : $user->password;
        $user->update($user_data);
    }

    public function updateInfo($request)
    {
        $id = Auth::user()->id;
        $user = User::find($id);
        $date = Carbon::now()->format('d-m-Y');
        $avatar = $request->avatar;
        if (isset($avatar)) {
            if (isset($user->avatar)) {
                unlink(public_path($user->avatar));
            }
            $avatar_name = 'upload/users/' . $date . '/' . Str::random(10) . rand() . '.' . $avatar->getClientOriginalExtension();
            $destinationPath = public_path('upload/users/' . $date);
            $avatar->move($destinationPath, $avatar_name);
            $user->avatar = $avatar_name;
        }
        $user->name = isset($request->name) ? $request->name : $user->name;
        $user->email = isset($request->email) ? $request->email : $user->email;
        $user->phone = isset($request->phone) ? $request->phone : $user->phone;
        $user->limit = isset($request->limit) ? $request->limit : $user->limit;
        $user->join_type = isset($request->join_type) ? $request->join_type : $user->join_type;
        $user->condition_type = isset($request->condition_type) ? implode(',',$request->condition_type) : $user->condition_type;
        $user->prize_type = isset($request->prize_type) ? implode(',',$request->prize_type) : $user->prize_type;
        $user->grab_condition = isset($request->grab_condition) ? implode(',',$request->grab_condition) : $user->grab_condition;
        $user->grab_able = isset($request->grab_able) ? implode(',',$request->grab_able) : $user->grab_able;
        $user->min_second = isset($request->min_second) ? $request->min_second : $user->min_second;
        $user->max_second = isset($request->max_second) ? $request->max_second : $user->max_second;
        $user->save();
        return $user;
    }

    public function updateSetting($request)
    {
        $user = User::find($request->id);
        $user->limit = $request->limit;
        $user->join_type = $request->join_type;
        $user->condition_type = !empty($request->condition_type) ? implode(',',$request->condition_type) : '';
        $user->prize_type = !empty($request->prize_type) ? implode(',',$request->prize_type) : '';
        $user->grab_condition = !empty($request->grab_condition) ? implode(',',$request->grab_condition) : '';
        $user->grab_able = !empty($request->grab_able) ? implode(',',$request->grab_able) : '';
        $user->share_room = !empty($request->share_room) ? implode(',',$request->share_room) : '';
        $user->min_second = $request->min_second;
        $user->max_second = $request->max_second;
        $user->coins = $request->coins;
        $user->save();
    }

    public function changePassword($request)
    {
        if (Hash::check($request->old_password, Auth::user()->password)) {
            $id = Auth::user()->id;
            $user = User::find($id);
            $user->password = Hash::make($request->password);
            $user->save();
            return true;
        }
        return false;
    }

    public function delete($id)
    {
        $user = User::find($id);
        if ($user->role == 1) {
            return response()->json([
                    'success' => false,
                    'msg' => 'Admin không thế xóa'
            ]);
        }
        $user->delete();
        return response()->json([
                'success' => true,
                'msg' => 'Xóa thành viên thành công'
        ]);
    }
}
