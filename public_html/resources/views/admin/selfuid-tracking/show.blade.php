@extends('layouts.master')

@section('title')
    <title>Chi tiết SelfUID: {{ $selfUid }} - Admin Panel</title>
@endsection

@section('style')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@endsection

@section('content')
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Chi tiết SelfUID: {{ $selfUid }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('selfuid-tracking.index') }}">SelfUID Tracking</a></li>
                        <li class="breadcrumb-item active">{{ $selfUid }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ $stats['total_requests'] }}</h3>
                            <p>Tổng số requests</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ number_format($stats['current_diamond']) }}</h3>
                            <p>Diamond hiện tại</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-gem"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ number_format($stats['current_bean']) }}</h3>
                            <p>Bean hiện tại</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ $stats['current_room_count'] }}</h3>
                            <p>Room Count hiện tại</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-home"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chart -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Biểu đồ thay đổi theo thời gian</h3>
                    <div class="card-tools">
                        <select id="chartDays" class="form-control form-control-sm" style="width: auto;">
                            <option value="1">24 giờ qua</option>
                            <option value="7" selected>7 ngày qua</option>
                            <option value="30">30 ngày qua</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="balanceChart" height="100"></canvas>
                </div>
            </div>

            <!-- Filter Form -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Bộ lọc lịch sử</h3>
                </div>
                <div class="card-body">
                    <form method="GET">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>User Token</label>
                                    <input type="text" name="user_token" class="form-control" 
                                           value="{{ request('user_token') }}" placeholder="User Token">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Từ ngày</label>
                                    <input type="date" name="date_from" class="form-control" 
                                           value="{{ request('date_from') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Đến ngày</label>
                                    <input type="date" name="date_to" class="form-control" 
                                           value="{{ request('date_to') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Lọc
                                        </button>
                                        <a href="{{ route('selfuid-tracking.show', $selfUid) }}" class="btn btn-secondary">
                                            <i class="fas fa-undo"></i> Reset
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- History Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Lịch sử thay đổi</h3>
                    <div class="card-tools">
                        <span class="badge badge-info">{{ $history->total() }} bản ghi</span>
                        <small class="text-muted ml-2">
                            <i class="fas fa-info-circle"></i>
                            Chỉ hiển thị khi có thay đổi số liệu
                        </small>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Thời gian</th>
                                    <th>User Token</th>
                                    <th>Diamond</th>
                                    <th>Bean</th>
                                    <th>Room Count</th>
                                    <th>Client</th>
                                    <th>IP Address</th>
                                    <th>Raw Self ID</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($history as $item)
                                    <tr>
                                        <td>
                                            <small>{{ $item->created_at->format('d/m/Y H:i:s') }}</small>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">{{ $item->user_token }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">
                                                {{ number_format($item->diamond_balance) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-success">
                                                {{ number_format($item->bean_balance) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">
                                                {{ $item->room_count }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($item->is_old_client ?? false)
                                                <span class="badge badge-warning" title="{{ $item->client_notes }}">
                                                    <i class="fas fa-exclamation-triangle"></i> Cũ
                                                </span>
                                            @else
                                                <span class="badge badge-success">
                                                    <i class="fas fa-check"></i> Mới
                                                </span>
                                            @endif
                                        </td>
                                        <td>{{ $item->ip_address }}</td>
                                        <td>
                                            <small class="text-muted">{{ $item->raw_self_id }}</small>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    {{ $history->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@section('script')
<script>
let balanceChart;

function loadChart(days = 7) {
    fetch(`/admin/selfuid-tracking/chart-data/{{ $selfUid }}?days=${days}`)
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('balanceChart').getContext('2d');

            if (balanceChart) {
                balanceChart.destroy();
            }

            balanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [
                        {
                            label: 'Diamond',
                            data: data.diamond,
                            borderColor: 'rgb(255, 193, 7)',
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            tension: 0.1
                        },
                        {
                            label: 'Bean',
                            data: data.bean,
                            borderColor: 'rgb(40, 167, 69)',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            tension: 0.1
                        },
                        {
                            label: 'Room Count',
                            data: data.room_count,
                            borderColor: 'rgb(220, 53, 69)',
                            backgroundColor: 'rgba(220, 53, 69, 0.1)',
                            tension: 0.1,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Diamond / Bean'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Room Count'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        });
}

$(document).ready(function() {
    loadChart();

    $('#chartDays').change(function() {
        loadChart($(this).val());
    });
});
</script>
@endsection
